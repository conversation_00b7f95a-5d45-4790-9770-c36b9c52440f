{"name": "my-electron-app", "version": "1.0.0", "description": "A budgeting app built with Electron", "homepage": "https://github.com/iamthebesthackerandcoder/todo#readme", "bugs": {"url": "https://github.com/iamthebesthackerandcoder/todo/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/iamthebesthackerandcoder/todo.git"}, "license": "ISC", "author": "", "type": "commonjs", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}, "build": {"appId": "com.example.my-electron-app", "productName": "Budgeting App", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "index.html", "renderer.js", "styles.css", "assets/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": [{"target": "zip", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.finance"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "publish": {"provider": "github", "owner": "iamthebesthackerandcoder", "repo": "todo"}}, "dependencies": {"chart.js": "^4.5.0", "electron-updater": "^6.6.2"}}