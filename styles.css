/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    overflow: hidden;
}

#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.app-title {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 600;
}

.app-icon {
    margin-right: 0.5rem;
    font-size: 1.8rem;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background-color: #45a049;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

/* Enhanced Save Button */
#save-btn {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
    transition: all 0.3s ease;
}

#save-btn:hover {
    background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

#save-btn:disabled {
    background: #ccc;
    color: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

#save-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 300px;
    background-color: white;
    border-right: 1px solid #e0e0e0;
    padding: 1.5rem;
    overflow-y: auto;
}

.sidebar-section {
    margin-bottom: 2rem;
}

.sidebar-section h3 {
    margin-bottom: 1rem;
    color: #555;
    font-size: 1.1rem;
}

.categories-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.category-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.category-item:hover {
    background-color: #f8f9fa;
}

.category-item.active {
    background-color: #e3f2fd;
    border-color: #2196F3;
}

.category-icon {
    margin-right: 0.75rem;
    font-size: 1.2rem;
}

.category-name {
    flex: 1;
    font-weight: 500;
}

.category-amount {
    font-weight: 600;
    color: #666;
}

/* Summary Card */
.summary-card {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.summary-item:last-child {
    margin-bottom: 0;
    padding-top: 0.5rem;
    border-top: 1px solid #dee2e6;
    font-weight: 600;
}

.summary-value.positive {
    color: #4CAF50;
}

.summary-value.negative {
    color: #f44336;
}

/* Content Area */
.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: white;
    margin: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.content-header h2 {
    color: #333;
    font-size: 1.4rem;
}

.content-body {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
}

/* Items Container */
.items-container {
    height: 100%;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #666;
    text-align: center;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: #888;
}

.empty-state p {
    color: #aaa;
}

/* Item List */
.item-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #2196F3;
}

.item-info {
    flex: 1;
}

.item-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.item-description {
    color: #666;
    font-size: 0.9rem;
}

.item-amount {
    font-weight: 700;
    font-size: 1.1rem;
    color: #4CAF50;
}

.item.expense .item-amount {
    color: #f44336;
}

.item-actions {
    display: flex;
    gap: 0.5rem;
    margin-left: 1rem;
}

.btn-small {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: #333;
}

.modal-body {
    padding: 1.5rem;
}

/* Form */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2196F3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

/* Status Bar */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    font-size: 0.85rem;
    color: #666;
    transition: all 0.3s ease;
}

.status-unsaved {
    color: #ff9800;
    font-weight: 600;
}

/* Success/Error Status Messages */
#status-message {
    transition: all 0.3s ease;
}

#status-message:contains("✅") {
    color: #4CAF50;
    font-weight: 600;
}

#status-message:contains("❌") {
    color: #f44336;
    font-weight: 600;
}

/* New Feature Styles */

/* Quick Actions */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.btn-small {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
}

/* Filter Controls */
.filter-controls {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.filter-select,
.search-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.search-input {
    background-color: #f8f9fa;
}

.search-input:focus {
    outline: none;
    border-color: #2196F3;
    background-color: white;
}

/* Content Header Updates */
.content-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.content-header-left h2 {
    margin: 0 0 0.5rem 0;
    color: #333;
    font-size: 1.4rem;
}

.category-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #666;
}

.content-header-right {
    display: flex;
    gap: 0.5rem;
}

/* Category Budget Info */
.category-budget {
    font-size: 0.8rem;
    color: #888;
    margin-left: auto;
}

.category-item.over-budget {
    border-color: #f44336;
    background-color: #ffebee;
}

.category-item.near-budget {
    border-color: #ff9800;
    background-color: #fff3e0;
}

/* Enhanced Item Display */
.item {
    position: relative;
}

.item.recurring::before {
    content: "🔄";
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    font-size: 0.8rem;
    opacity: 0.6;
}

.item-tags {
    margin-top: 0.25rem;
    display: flex;
    gap: 0.25rem;
    flex-wrap: wrap;
}

.item-tag {
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 0.1rem 0.4rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.item-meta {
    margin-top: 0.5rem;
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    color: #888;
}

.item-date,
.item-recurring {
    display: flex;
    align-items: center;
}

.item-recurring {
    font-weight: 500;
    color: #2196F3;
}

/* Modal Enhancements */
.modal-large {
    width: 95%;
    max-width: 900px;
    max-height: 95vh;
}

/* Chart Styles */
.chart-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.chart-tab {
    padding: 0.75rem 1rem;
    border: none;
    background: none;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    font-weight: 500;
    color: #666;
    transition: all 0.2s ease;
}

.chart-tab.active {
    color: #2196F3;
    border-bottom-color: #2196F3;
}

.chart-tab:hover {
    color: #2196F3;
    background-color: #f5f5f5;
}

.chart-container {
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 8px;
}

/* Form Enhancements */
.radio-group,
.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.radio-group label,
.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: normal;
    cursor: pointer;
}

.date-range {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.date-range input {
    flex: 1;
}

.date-range span {
    color: #666;
    font-weight: 500;
}

/* Budget Status Indicators */
.budget-progress {
    width: 100%;
    height: 4px;
    background-color: #e0e0e0;
    border-radius: 2px;
    margin-top: 0.25rem;
    overflow: hidden;
}

.budget-progress-bar {
    height: 100%;
    background-color: #4CAF50;
    transition: all 0.3s ease;
}

.budget-progress-bar.warning {
    background-color: #ff9800;
}

.budget-progress-bar.danger {
    background-color: #f44336;
}

/* Alert Styles */
.alert {
    padding: 0.75rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.alert-info {
    background-color: #e3f2fd;
    border-color: #2196F3;
    color: #1976d2;
}

.alert-warning {
    background-color: #fff3e0;
    border-color: #ff9800;
    color: #f57c00;
}

.alert-danger {
    background-color: #ffebee;
    border-color: #f44336;
    color: #d32f2f;
}

/* Notifications */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 2000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.notification {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid;
    min-width: 300px;
    max-width: 400px;
    animation: slideIn 0.3s ease-out;
}

.notification-info {
    border-left-color: #2196F3;
}

.notification-warning {
    border-left-color: #ff9800;
}

.notification-success {
    border-left-color: #4CAF50;
}

.notification-error {
    border-left-color: #f44336;
}

.notification-content {
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification-message {
    flex: 1;
    font-size: 14px;
    color: #333;
}

.notification-action {
    background: #2196F3;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Recurring Items */
.recurring-due-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    margin-bottom: 8px;
    background: #f8f9fa;
}

.recurring-due-item .item-info {
    flex: 1;
}

.recurring-due-item .item-details {
    font-size: 0.85rem;
    color: #666;
    margin-top: 4px;
}

.recurring-due-item .item-actions {
    display: flex;
    gap: 8px;
}

/* Status Updates */
.status-unsaved {
    color: #ff9800 !important;
    font-weight: 500;
}

.status-unsaved::before {
    content: "● ";
    color: #ff9800;
}

/* Enhanced Form Validation */
.form-group.error input,
.form-group.error select,
.form-group.error textarea {
    border-color: #f44336;
    background-color: #ffebee;
}

.form-error {
    color: #f44336;
    font-size: 0.8rem;
    margin-top: 0.25rem;
    display: block;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2196F3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Enhanced Tooltips */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
}

.tooltip:hover::before {
    opacity: 1;
}

/* Responsive */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        max-height: 200px;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .header-actions {
        justify-content: center;
    }

    .content-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .content-header-right {
        justify-content: flex-end;
    }

    .category-stats {
        flex-direction: column;
        gap: 0.25rem;
    }

    .date-range {
        flex-direction: column;
        align-items: stretch;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}