<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy"
        content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';">
    <title>My Electron App - Budgeting Tool</title>
    <link rel="stylesheet" href="styles.css">
</head>

<body>
    <div id="app">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="app-icon">💰</span>
                    My Budget App
                </h1>
                <div class="header-actions">
                    <button id="search-btn" class="btn btn-secondary">🔍 Search</button>
                    <button id="charts-btn" class="btn btn-secondary">📊 Charts</button>
                    <button id="export-btn" class="btn btn-secondary">📤 Export</button>
                    <button id="settings-btn" class="btn btn-secondary">⚙️ Settings</button>
                    <button id="new-btn" class="btn btn-primary">New Budget</button>
                    <button id="save-btn" class="btn btn-primary">💾 Save Budget</button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-section">
                    <h3>Categories</h3>
                    <div id="categories-list" class="categories-list">
                        <div class="category-item active" data-category="income">
                            <span class="category-icon">💵</span>
                            <span class="category-name">Income</span>
                            <span class="category-amount">$0.00</span>
                            <span class="category-budget"></span>
                        </div>
                        <div class="category-item" data-category="expenses">
                            <span class="category-icon">💸</span>
                            <span class="category-name">Expenses</span>
                            <span class="category-amount">$0.00</span>
                            <span class="category-budget"></span>
                        </div>
                        <div class="category-item" data-category="bills">
                            <span class="category-icon">🧾</span>
                            <span class="category-name">Bills</span>
                            <span class="category-amount">$0.00</span>
                            <span class="category-budget"></span>
                        </div>
                        <div class="category-item" data-category="savings">
                            <span class="category-icon">🏦</span>
                            <span class="category-name">Savings</span>
                            <span class="category-amount">$0.00</span>
                            <span class="category-budget"></span>
                        </div>
                        <div class="category-item" data-category="investments">
                            <span class="category-icon">📈</span>
                            <span class="category-name">Investments</span>
                            <span class="category-amount">$0.00</span>
                            <span class="category-budget"></span>
                        </div>
                        <div class="category-item" data-category="debt">
                            <span class="category-icon">💳</span>
                            <span class="category-name">Debt</span>
                            <span class="category-amount">$0.00</span>
                            <span class="category-budget"></span>
                        </div>
                        <div class="category-item" data-category="goals">
                            <span class="category-icon">🎯</span>
                            <span class="category-name">Goals</span>
                            <span class="category-amount">$0.00</span>
                            <span class="category-budget"></span>
                        </div>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3>Quick Actions</h3>
                    <div class="quick-actions">
                        <button id="add-recurring-btn" class="btn btn-small btn-primary">+ Recurring Item</button>
                        <button id="set-budget-btn" class="btn btn-small btn-secondary">Set Budget Limit</button>
                        <button id="backup-btn" class="btn btn-small btn-secondary">Backup Data</button>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3>Filters</h3>
                    <div class="filter-controls">
                        <select id="date-filter" class="filter-select">
                            <option value="all">All Time</option>
                            <option value="this-month">This Month</option>
                            <option value="last-month">Last Month</option>
                            <option value="this-year">This Year</option>
                            <option value="custom">Custom Range</option>
                        </select>
                        <input type="text" id="search-input" placeholder="Search items..." class="search-input">
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3>Summary</h3>
                    <div class="summary-card">
                        <div class="summary-item">
                            <span class="summary-label">Total Income:</span>
                            <span id="total-income" class="summary-value positive">$0.00</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Total Expenses:</span>
                            <span id="total-expenses" class="summary-value negative">$0.00</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Total Bills:</span>
                            <span id="total-bills" class="summary-value negative">$0.00</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Net Balance:</span>
                            <span id="net-balance" class="summary-value">$0.00</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Budget Status:</span>
                            <span id="budget-status" class="summary-value">On Track</span>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Content Area -->
            <section class="content-area">
                <div class="content-header">
                    <div class="content-header-left">
                        <h2 id="current-category">Income</h2>
                        <div class="category-stats">
                            <span id="category-count">0 items</span>
                            <span id="category-budget-info"></span>
                        </div>
                    </div>
                    <div class="content-header-right">
                        <button id="bulk-actions-btn" class="btn btn-secondary">Bulk Actions</button>
                        <button id="add-item-btn" class="btn btn-primary">Add Item</button>
                    </div>
                </div>

                <div class="content-body">
                    <!-- Items List -->
                    <div id="items-container" class="items-container">
                        <div class="empty-state">
                            <div class="empty-icon">📊</div>
                            <h3>No items yet</h3>
                            <p>Click "Add Item" to get started with your budget</p>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Add Item Modal -->
        <div id="add-item-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Add New Item</h3>
                    <button id="close-modal" class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="add-item-form">
                        <div class="form-group">
                            <label for="item-name">Name:</label>
                            <input type="text" id="item-name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="item-amount">Amount:</label>
                            <input type="number" id="item-amount" name="amount" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="item-category">Category:</label>
                            <select id="item-category" name="category" required>
                                <option value="income">Income</option>
                                <option value="expenses">Expenses</option>
                                <option value="bills">Bills</option>
                                <option value="savings">Savings</option>
                                <option value="investments">Investments</option>
                                <option value="debt">Debt</option>
                                <option value="goals">Goals</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="item-date">Date:</label>
                            <input type="date" id="item-date" name="date" required>
                        </div>
                        <div class="form-group">
                            <label for="item-recurring">Recurring:</label>
                            <select id="item-recurring" name="recurring">
                                <option value="none">One-time</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                                <option value="yearly">Yearly</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="item-tags">Tags (comma-separated):</label>
                            <input type="text" id="item-tags" name="tags" placeholder="e.g., groceries, utilities">
                        </div>
                        <div class="form-group">
                            <label for="item-description">Description (optional):</label>
                            <textarea id="item-description" name="description" rows="3"></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="button" id="cancel-btn" class="btn btn-secondary">Cancel</button>
                            <button type="submit" class="btn btn-primary">Add Item</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Charts Modal -->
        <div id="charts-modal" class="modal">
            <div class="modal-content modal-large">
                <div class="modal-header">
                    <h3>Financial Charts</h3>
                    <button id="close-charts-modal" class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="chart-tabs">
                        <button class="chart-tab active" data-chart="overview">Overview</button>
                        <button class="chart-tab" data-chart="trends">Trends</button>
                        <button class="chart-tab" data-chart="categories">Categories</button>
                    </div>
                    <div id="chart-container" class="chart-container">
                        <canvas id="main-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Modal -->
        <div id="settings-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Settings</h3>
                    <button id="close-settings-modal" class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="currency-select">Currency:</label>
                        <select id="currency-select">
                            <option value="USD">USD ($)</option>
                            <option value="EUR">EUR (€)</option>
                            <option value="GBP">GBP (£)</option>
                            <option value="JPY">JPY (¥)</option>
                            <option value="CAD">CAD (C$)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="date-format-select">Date Format:</label>
                        <select id="date-format-select">
                            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                            <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="auto-backup-checkbox"> Enable Auto Backup
                        </label>
                    </div>
                    <div class="form-actions">
                        <button type="button" id="reset-settings-btn" class="btn btn-secondary">Reset to
                            Default</button>
                        <button type="button" id="save-settings-btn" class="btn btn-primary">Save Settings</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Budget Limit Modal -->
        <div id="budget-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Set Budget Limit</h3>
                    <button id="close-budget-modal" class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="budget-form">
                        <div class="form-group">
                            <label for="budget-category">Category:</label>
                            <select id="budget-category" name="category" required>
                                <option value="expenses">Expenses</option>
                                <option value="bills">Bills</option>
                                <option value="savings">Savings</option>
                                <option value="investments">Investments</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="budget-limit">Monthly Limit:</label>
                            <input type="number" id="budget-limit" name="limit" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="budget-alert">Alert at (%):</label>
                            <input type="number" id="budget-alert" name="alert" min="1" max="100" value="80">
                        </div>
                        <div class="form-actions">
                            <button type="button" id="cancel-budget-btn" class="btn btn-secondary">Cancel</button>
                            <button type="submit" class="btn btn-primary">Set Limit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Export Modal -->
        <div id="export-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Export Data</h3>
                    <button id="close-export-modal" class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>Export Format:</label>
                        <div class="radio-group">
                            <label><input type="radio" name="export-format" value="csv" checked> CSV</label>
                            <label><input type="radio" name="export-format" value="json"> JSON</label>
                            <label><input type="radio" name="export-format" value="pdf"> PDF Report</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Date Range:</label>
                        <div class="date-range">
                            <input type="date" id="export-start-date">
                            <span>to</span>
                            <input type="date" id="export-end-date">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Categories:</label>
                        <div class="checkbox-group">
                            <label><input type="checkbox" name="export-categories" value="income" checked>
                                Income</label>
                            <label><input type="checkbox" name="export-categories" value="expenses" checked>
                                Expenses</label>
                            <label><input type="checkbox" name="export-categories" value="bills" checked> Bills</label>
                            <label><input type="checkbox" name="export-categories" value="savings" checked>
                                Savings</label>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="button" id="cancel-export-btn" class="btn btn-secondary">Cancel</button>
                        <button type="button" id="export-btn-confirm" class="btn btn-primary">Export</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <footer class="status-bar">
            <div class="status-left">
                <span id="status-message">Ready</span>
            </div>
            <div class="status-right">
                <span id="app-version">v1.0.0</span>
            </div>
        </footer>
    </div>

    <script src="node_modules/chart.js/dist/chart.umd.js"></script>
    <script src="renderer.js"></script>
</body>

</html>